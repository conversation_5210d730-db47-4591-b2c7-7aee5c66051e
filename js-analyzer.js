const fs = require('fs');
const path = require('path');

/**
 * JavaScript文件分析器
 * 用于分析Cocos Creator编译后的JavaScript文件中的定义类型
 */
class JSFileAnalyzer {
    constructor() {
        this.analysisResult = {
            filePath: '',
            fileContent: '',
            definitions: [],
            imports: [],
            exports: [],
            globalFunctions: [],
            enums: [],
            hasModuleExports: false,
            analysisErrors: []
        };
    }

    /**
     * 分析JavaScript文件
     * @param {string} filePath 文件路径
     * @returns {Object} 分析结果
     */
    analyzeFile(filePath) {
        try {
            this.analysisResult.filePath = filePath;
            this.analysisResult.fileContent = fs.readFileSync(filePath, 'utf8');
            
            console.log(`\n=== 分析文件: ${filePath} ===`);
            
            this._analyzeImports();
            this._analyzeExports();
            this._analyzeDefinitions();
            this._analyzeEnums();
            this._analyzeGlobalFunctions();
            
            this._printAnalysisResult();
            return this.analysisResult;
            
        } catch (error) {
            this.analysisResult.analysisErrors.push(`文件读取错误: ${error.message}`);
            console.error(`分析文件失败: ${filePath}`, error);
            return this.analysisResult;
        }
    }

    /**
     * 分析导入语句
     */
    _analyzeImports() {
        const content = this.analysisResult.fileContent;
        
        // 匹配 require 语句
        const requirePattern = /var\s+(\$\d*\$?\w+)\s*=\s*require\("([^"]+)"\)/g;
        let match;
        
        while ((match = requirePattern.exec(content)) !== null) {
            this.analysisResult.imports.push({
                variableName: match[1],
                modulePath: match[2],
                type: 'require'
            });
        }
    }

    /**
     * 分析导出语句
     */
    _analyzeExports() {
        const content = this.analysisResult.fileContent;
        
        // 检查是否有 module.exports 模式
        if (content.includes('Object.defineProperty(exports, "__esModule"')) {
            this.analysisResult.hasModuleExports = true;
        }
        
        // 匹配 exports.xxx = 语句
        const exportsPattern = /exports\.(\w+)\s*=\s*([^;]+);/g;
        let match;
        
        while ((match = exportsPattern.exec(content)) !== null) {
            this.analysisResult.exports.push({
                name: match[1],
                value: match[2].trim(),
                type: 'export'
            });
        }
    }

    /**
     * 分析类定义
     */
    _analyzeDefinitions() {
        const content = this.analysisResult.fileContent;
        
        // 分析 ccclass 装饰器模式的类
        this._analyzeCCClasses(content);
        
        // 分析普通的类继承模式
        this._analyzeRegularClasses(content);
        
        // 分析静态类/对象模式
        this._analyzeStaticClasses(content);
    }

    /**
     * 分析 ccclass 装饰器的类
     */
    _analyzeCCClasses(content) {
        // 匹配 cc__decorate([ccp_ccclass("ClassName")], _ctor) 模式
        const ccclassPattern = /cc__decorate\(\[ccp_ccclass\("([^"]+)"\)\],\s*(\w+)\)/g;
        let match;
        
        while ((match = ccclassPattern.exec(content)) !== null) {
            const className = match[1];
            const constructorName = match[2];
            
            // 查找对应的构造函数定义
            const classDefPattern = new RegExp(`var\\s+exp_${className}\\s*=\\s*function\\s*\\([^)]*\\)\\s*{[^}]*}\\([^)]+\\)`);
            const classDefMatch = content.match(classDefPattern);
            
            this.analysisResult.definitions.push({
                name: className,
                type: 'ccclass',
                constructorName: constructorName,
                hasDecorator: true,
                definition: classDefMatch ? classDefMatch[0] : null
            });
        }
    }

    /**
     * 分析普通类继承
     */
    _analyzeRegularClasses(content) {
        // 匹配 var exp_ClassName = function (e) { ... }(BaseClass) 模式
        const classPattern = /var\s+exp_(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?}\s*\(\$\d*\$?\w+\.[\w.]+\)/g;
        let match;
        
        while ((match = classPattern.exec(content)) !== null) {
            const className = match[1];
            
            // 检查是否已经被识别为 ccclass
            const alreadyIdentified = this.analysisResult.definitions.some(def => def.name === className);
            if (!alreadyIdentified) {
                this.analysisResult.definitions.push({
                    name: className,
                    type: 'class',
                    hasDecorator: false,
                    definition: match[0]
                });
            }
        }
    }

    /**
     * 分析静态类/对象
     */
    _analyzeStaticClasses(content) {
        // 匹配直接的对象定义模式
        const staticPattern = /var\s+(\w+)\s*=\s*{[\s\S]*?};/g;
        let match;
        
        while ((match = staticPattern.exec(content)) !== null) {
            const objectName = match[1];
            
            // 排除一些常见的非类对象
            if (!['cc__decorator', '_', 'i'].includes(objectName)) {
                this.analysisResult.definitions.push({
                    name: objectName,
                    type: 'static_object',
                    definition: match[0]
                });
            }
        }
    }

    /**
     * 分析枚举定义
     */
    _analyzeEnums() {
        const content = this.analysisResult.fileContent;
        
        // 匹配枚举模式 e[e.VALUE = number] = "VALUE"
        const enumPattern = /\(function\s*\(e\)\s*{[\s\S]*?}\)\([\w.]+\s*\|\|\s*\([\w.]+\s*=\s*{}\)\)/g;
        let match;
        
        while ((match = enumPattern.exec(content)) !== null) {
            // 提取枚举值
            const enumContent = match[0];
            const enumValuePattern = /e\[e\.(\w+)\s*=\s*(\d+)\]\s*=\s*"(\w+)"/g;
            let valueMatch;
            const enumValues = [];
            
            while ((valueMatch = enumValuePattern.exec(enumContent)) !== null) {
                enumValues.push({
                    name: valueMatch[1],
                    value: parseInt(valueMatch[2]),
                    stringValue: valueMatch[3]
                });
            }
            
            if (enumValues.length > 0) {
                this.analysisResult.enums.push({
                    type: 'enum',
                    values: enumValues,
                    definition: enumContent
                });
            }
        }
    }

    /**
     * 分析全局函数
     */
    _analyzeGlobalFunctions() {
        const content = this.analysisResult.fileContent;
        
        // 匹配 window.functionName = function 模式
        const globalFuncPattern = /window\.(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?};/g;
        let match;
        
        while ((match = globalFuncPattern.exec(content)) !== null) {
            this.analysisResult.globalFunctions.push({
                name: match[1],
                type: 'global_function',
                definition: match[0]
            });
        }
    }

    /**
     * 打印分析结果
     */
    _printAnalysisResult() {
        const result = this.analysisResult;
        
        console.log(`\n📁 文件: ${path.basename(result.filePath)}`);
        console.log(`📏 文件大小: ${result.fileContent.length} 字符`);
        
        if (result.imports.length > 0) {
            console.log(`\n📥 导入 (${result.imports.length}个):`);
            result.imports.forEach(imp => {
                console.log(`  - ${imp.variableName} <- "${imp.modulePath}"`);
            });
        }
        
        if (result.exports.length > 0) {
            console.log(`\n📤 导出 (${result.exports.length}个):`);
            result.exports.forEach(exp => {
                console.log(`  - ${exp.name} = ${exp.value.substring(0, 50)}${exp.value.length > 50 ? '...' : ''}`);
            });
        }
        
        if (result.definitions.length > 0) {
            console.log(`\n🏗️ 类定义 (${result.definitions.length}个):`);
            result.definitions.forEach(def => {
                const decorator = def.hasDecorator ? ' [装饰器]' : '';
                console.log(`  - ${def.name} (${def.type})${decorator}`);
            });
        }
        
        if (result.enums.length > 0) {
            console.log(`\n🔢 枚举 (${result.enums.length}个):`);
            result.enums.forEach((enumDef, index) => {
                console.log(`  - 枚举${index + 1}: ${enumDef.values.map(v => v.name).join(', ')}`);
            });
        }
        
        if (result.globalFunctions.length > 0) {
            console.log(`\n🌐 全局函数 (${result.globalFunctions.length}个):`);
            result.globalFunctions.forEach(func => {
                console.log(`  - window.${func.name}`);
            });
        }
        
        if (result.analysisErrors.length > 0) {
            console.log(`\n❌ 分析错误:`);
            result.analysisErrors.forEach(error => {
                console.log(`  - ${error}`);
            });
        }
        
        console.log(`\n📊 总结:`);
        console.log(`  - 类定义: ${result.definitions.length}个`);
        console.log(`  - 枚举: ${result.enums.length}个`);
        console.log(`  - 全局函数: ${result.globalFunctions.length}个`);
        console.log(`  - 导入: ${result.imports.length}个`);
        console.log(`  - 导出: ${result.exports.length}个`);
    }
}

/**
 * 批量分析目录中的所有JavaScript文件
 * @param {string} dirPath 目录路径
 * @returns {Array} 所有文件的分析结果
 */
function batchAnalyzeDirectory(dirPath) {
    const analyzer = new JSFileAnalyzer();
    const results = [];

    console.log(`\n🔍 开始批量分析目录: ${dirPath}`);

    try {
        const files = fs.readdirSync(dirPath);
        const jsFiles = files.filter(file => file.endsWith('.js'));

        console.log(`📁 找到 ${jsFiles.length} 个JavaScript文件`);

        jsFiles.forEach((file, index) => {
            const filePath = path.join(dirPath, file);
            console.log(`\n[${index + 1}/${jsFiles.length}] 分析: ${file}`);

            const result = analyzer.analyzeFile(filePath);
            results.push(result);

            // 重置分析器状态
            analyzer.analysisResult = {
                filePath: '',
                fileContent: '',
                definitions: [],
                imports: [],
                exports: [],
                globalFunctions: [],
                enums: [],
                hasModuleExports: false,
                analysisErrors: []
            };
        });

        // 打印总体统计
        printBatchSummary(results);

    } catch (error) {
        console.error(`批量分析失败: ${error.message}`);
    }

    return results;
}

/**
 * 打印批量分析的总体统计
 */
function printBatchSummary(results) {
    console.log(`\n\n📊 ===== 批量分析总结 =====`);

    const stats = {
        totalFiles: results.length,
        filesWithClasses: 0,
        filesWithEnums: 0,
        filesWithGlobalFunctions: 0,
        filesWithExportsOnly: 0,
        totalClasses: 0,
        totalEnums: 0,
        totalGlobalFunctions: 0,
        ccclassCount: 0,
        regularClassCount: 0,
        staticObjectCount: 0
    };

    const fileTypes = {
        'single_class': [],
        'multi_class': [],
        'enum_only': [],
        'config_only': [],
        'global_functions': [],
        'mixed': []
    };

    results.forEach(result => {
        const fileName = path.basename(result.filePath);
        const classCount = result.definitions.length;
        const enumCount = result.enums.length;
        const globalFuncCount = result.globalFunctions.length;
        const exportCount = result.exports.length;

        // 统计数据
        if (classCount > 0) stats.filesWithClasses++;
        if (enumCount > 0) stats.filesWithEnums++;
        if (globalFuncCount > 0) stats.filesWithGlobalFunctions++;
        if (exportCount > 0 && classCount === 0 && enumCount === 0 && globalFuncCount === 0) {
            stats.filesWithExportsOnly++;
        }

        stats.totalClasses += classCount;
        stats.totalEnums += enumCount;
        stats.totalGlobalFunctions += globalFuncCount;

        // 统计类类型
        result.definitions.forEach(def => {
            if (def.type === 'ccclass') stats.ccclassCount++;
            else if (def.type === 'class') stats.regularClassCount++;
            else if (def.type === 'static_object') stats.staticObjectCount++;
        });

        // 分类文件类型
        if (classCount === 1 && enumCount === 0 && globalFuncCount === 0) {
            fileTypes.single_class.push(fileName);
        } else if (classCount > 1 && enumCount === 0 && globalFuncCount === 0) {
            fileTypes.multi_class.push(fileName);
        } else if (classCount === 0 && enumCount > 0 && globalFuncCount === 0) {
            fileTypes.enum_only.push(fileName);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount === 0 && exportCount > 0) {
            fileTypes.config_only.push(fileName);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount > 0) {
            fileTypes.global_functions.push(fileName);
        } else if (classCount > 0 || enumCount > 0 || globalFuncCount > 0) {
            fileTypes.mixed.push(fileName);
        }
    });

    console.log(`📁 总文件数: ${stats.totalFiles}`);
    console.log(`🏗️ 包含类的文件: ${stats.filesWithClasses} (${(stats.filesWithClasses/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`🔢 包含枚举的文件: ${stats.filesWithEnums} (${(stats.filesWithEnums/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`🌐 包含全局函数的文件: ${stats.filesWithGlobalFunctions} (${(stats.filesWithGlobalFunctions/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`⚙️ 仅配置文件: ${stats.filesWithExportsOnly} (${(stats.filesWithExportsOnly/stats.totalFiles*100).toFixed(1)}%)`);

    console.log(`\n📈 定义统计:`);
    console.log(`  - 总类数: ${stats.totalClasses}`);
    console.log(`    - CCClass: ${stats.ccclassCount}`);
    console.log(`    - 普通类: ${stats.regularClassCount}`);
    console.log(`    - 静态对象: ${stats.staticObjectCount}`);
    console.log(`  - 总枚举数: ${stats.totalEnums}`);
    console.log(`  - 总全局函数数: ${stats.totalGlobalFunctions}`);

    console.log(`\n📋 文件类型分布:`);
    console.log(`  - 单类文件 (${fileTypes.single_class.length}个): ${fileTypes.single_class.slice(0, 5).join(', ')}${fileTypes.single_class.length > 5 ? '...' : ''}`);
    console.log(`  - 多类文件 (${fileTypes.multi_class.length}个): ${fileTypes.multi_class.slice(0, 5).join(', ')}${fileTypes.multi_class.length > 5 ? '...' : ''}`);
    console.log(`  - 枚举文件 (${fileTypes.enum_only.length}个): ${fileTypes.enum_only.slice(0, 5).join(', ')}${fileTypes.enum_only.length > 5 ? '...' : ''}`);
    console.log(`  - 配置文件 (${fileTypes.config_only.length}个): ${fileTypes.config_only.slice(0, 5).join(', ')}${fileTypes.config_only.length > 5 ? '...' : ''}`);
    console.log(`  - 全局函数文件 (${fileTypes.global_functions.length}个): ${fileTypes.global_functions.slice(0, 5).join(', ')}${fileTypes.global_functions.length > 5 ? '...' : ''}`);
    console.log(`  - 混合类型文件 (${fileTypes.mixed.length}个): ${fileTypes.mixed.slice(0, 5).join(', ')}${fileTypes.mixed.length > 5 ? '...' : ''}`);
}

// 如果直接运行此脚本
if (require.main === module) {
    const targetPath = process.argv[2];

    if (!targetPath) {
        console.log('使用方法:');
        console.log('  分析单个文件: node js-analyzer.js <文件路径>');
        console.log('  批量分析目录: node js-analyzer.js <目录路径>');
        console.log('示例:');
        console.log('  node js-analyzer.js scripts/activityCfg.js');
        console.log('  node js-analyzer.js scripts');
        process.exit(1);
    }

    const stats = fs.statSync(targetPath);

    if (stats.isDirectory()) {
        // 批量分析目录
        batchAnalyzeDirectory(targetPath);
    } else if (stats.isFile() && targetPath.endsWith('.js')) {
        // 分析单个文件
        const analyzer = new JSFileAnalyzer();
        analyzer.analyzeFile(targetPath);
    } else {
        console.error('错误: 请提供有效的JavaScript文件或目录路径');
        process.exit(1);
    }
}

module.exports = { JSFileAnalyzer, batchAnalyzeDirectory };
